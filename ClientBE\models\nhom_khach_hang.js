'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class NhomKhachHang extends Model {
    static associate(models) {
      // Quan hệ với người dùng (n-n)
      NhomKhachHang.belongsToMany(models.NguoiDung, {
        through: models.NhomKhachHangNguoiDung,
        foreignKey: 'nhom_khach_hang_id',
        otherKey: 'nguoi_dung_id',
        as: 'nguoiDungList'
      });
    }
  }

  NhomKhachHang.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    ten_nhom: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [2, 100]
      }
    },
    mo_ta: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    ma_nhom: {
      type: DataTypes.STRING,
      allowNull: true,
      unique: true
    },
    ngay_tao: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    loai_nhom: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 1,
      comment: '<PERSON><PERSON><PERSON> nhóm khách hàng, 1: cố định, 2: tự động'
    }
  }, {
    sequelize,
    modelName: 'NhomKhachHang',
    tableName: 'nhom_khach_hang',
    timestamps: false
  });

  return NhomKhachHang;
};
