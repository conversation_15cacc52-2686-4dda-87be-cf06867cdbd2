'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class KhoHang extends Model {
    static associate(models) {
      // Quan hệ với tồn kho (1-n)
      KhoHang.hasMany(models.TonKhoPhienBan, {
        foreignKey: 'kho_hang_id',
        as: 'tonKhoList'
      });

      // Quan hệ với lịch sử kho (1-n)
      KhoHang.hasMany(models.LichSuKho, {
        foreignKey: 'kho_hang_id',
        as: 'lichSuKhoList'
      });

      // Quan hệ với đơn hàng (1-n)
      KhoHang.hasMany(models.DonHang, {
        foreignKey: 'kho_hang_id',
        as: 'donHangList'
      });

      // Quan hệ với phiếu kiểm kê (1-n)
      if (models.PhieuKiemKe) {
        KhoHang.hasMany(models.PhieuKiemKe, {
          foreignKey: 'kho_hang_id',
          as: 'phieuKiemKeList'
        });
      }
    }
  }

  KhoHang.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    ten_kho: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [2, 100]
      }
    },
    dia_chi: {
      type: DataTypes.STRING,
      allowNull: true
    },
    nguoi_tao: {
      type: DataTypes.STRING,
      allowNull: true
    },
    nguoi_cap_nhap: {
      type: DataTypes.STRING,
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'KhoHang',
    tableName: 'kho_hang'
  });

  return KhoHang;
};
