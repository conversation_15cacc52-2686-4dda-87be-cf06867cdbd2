'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class DonHangTag extends Model {
    static associate(models) {
      // Quan hệ với đơn hàng
      DonHangTag.belongsTo(models.DonHang, {
        foreignKey: 'don_hang_id',
        as: 'donHang'
      });

      // Quan hệ với tag
      DonHangTag.belongsTo(models.Tag, {
        foreignKey: 'tag_id',
        as: 'tag'
      });
    }
  }

  DonHangTag.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    don_hang_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'don_hang',
        key: 'id'
      }
    },
    tag_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'tag',
        key: 'id'
      }
    }
  }, {
    sequelize,
    modelName: 'DonHangTag',
    tableName: 'don_hang_tag',
    timestamps: false,
    indexes: [
      {
        unique: true,
        fields: ['don_hang_id', 'tag_id'],
        name: 'don_hang_tag_unique'
      }
    ]
  });

  return DonHangTag;
};
