'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class NguoiDungVaiTro extends Model {
    static associate(models) {
      // Quan hệ với người dùng
      NguoiDungVaiTro.belongsTo(models.NguoiDung, {
        foreignKey: 'nguoi_dung_id',
        as: 'nguoiDung'
      });

      // Quan hệ với vai trò
      NguoiDungVaiTro.belongsTo(models.VaiTro, {
        foreignKey: 'vai_tro_id',
        as: 'vaiTro'
      });
    }
  }

  NguoiDungVaiTro.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    nguoi_dung_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'nguoi_dung',
        key: 'id'
      }
    },
    vai_tro_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'vai_tro',
        key: 'id'
      }
    }
  }, {
    sequelize,
    modelName: 'NguoiDungVaiTro',
    tableName: 'nguoi_dung_vai_tro',
    timestamps: false,
    indexes: [
      {
        unique: true,
        fields: ['nguoi_dung_id', 'vai_tro_id'],
        name: 'nd_vai_tro_unique'
      }
    ]
  });

  return NguoiDungVaiTro;
};
