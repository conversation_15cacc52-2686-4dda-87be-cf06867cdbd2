# Fashion Store - Frontend

Ứng dụng web bán hàng thời trang hiện đại được xây dựng với ReactJS và Vite.

## 🚀 Tính năng

### Đã hoàn thành:

- ✅ **Trang chủ (Home)**: Banner carousel, danh mục sản phẩm, sản phẩm nổi bật
- ✅ **Trang sản phẩm**: <PERSON><PERSON> sách sản phẩm với lọc, tì<PERSON> kiếm, phân trang
- ✅ **Chi tiết sản phẩm**: Gallery ảnh, thông tin chi tiết, chọn variant
- ✅ **Giỏ hàng**: <PERSON>u<PERSON><PERSON> lý sản phẩm, cập nhật số lượng
- ✅ **Thanh toán**: Form thông tin khách hàng, xác nhận đơn hàng
- ✅ **Responsive Design**: Tối ưu cho desktop và mobile
- ✅ **Dark Mode**: Chuyển đổi giao diện sáng/tối
- ✅ **State Management**: Context API cho cart và theme
- ✅ **API Integration**: Sẵn sàng kết nối với backend

### Tính năng nâng cao (có thể mở rộng):

- 🔄 Đăng nhập/đăng ký người dùng
- 🔄 Quản lý đơn hàng cho admin
- 🔄 Wishlist (danh sách yêu thích)
- 🔄 Reviews và ratings
- 🔄 Tích hợp thanh toán online

## 🛠️ Công nghệ sử dụng

- **Frontend Framework**: React 18 + Vite
- **Styling**: Tailwind CSS
- **Routing**: React Router DOM
- **State Management**: Context API + useReducer
- **HTTP Client**: Axios
- **Icons**: Lucide React
- **UI Components**: Custom components với Headless UI

## 📦 Cài đặt và chạy

### Yêu cầu hệ thống:

- Node.js >= 16.0.0
- npm hoặc yarn

### Cài đặt:

```bash
# Di chuyển vào thư mục dự án
cd ClientFE/fashion-store

# Cài đặt dependencies
npm install

# Chạy ứng dụng ở chế độ development
npm run dev

# Build cho production
npm run build

# Preview build
npm run preview
```

### Cấu hình:

Tạo file `.env` trong thư mục gốc:

```env
VITE_API_BASE_URL=http://localhost:3000/api
```

## 📁 Cấu trúc thư mục

```
src/
├── components/          # Components tái sử dụng
│   ├── ui/             # UI components cơ bản
│   ├── layout/         # Layout components
│   └── home/           # Components cho trang chủ
├── pages/              # Các trang chính
├── context/            # Context providers
├── hooks/              # Custom hooks
├── services/           # API services
├── utils/              # Utility functions
└── assets/             # Static assets
```

## 🎨 Thiết kế

### Color Palette:

- **Primary**: Orange (#f3770a - #431402)
- **Secondary**: Gray (#f8fafc - #020617)
- **Accent**: Tailwind default colors

### Typography:

- **Font Family**: Inter (Google Fonts)
- **Font Weights**: 300, 400, 500, 600, 700

### Responsive Breakpoints:

- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

## 🔌 API Integration

Ứng dụng được thiết kế để kết nối với backend NodeJS thông qua REST API:

### Endpoints chính:

- `GET /api/san-pham` - Lấy danh sách sản phẩm
- `GET /api/san-pham/:id` - Chi tiết sản phẩm
- `GET /api/loai-san-pham` - Danh mục sản phẩm
- `POST /api/don-hang` - Tạo đơn hàng

### Mock Data:

Khi backend chưa sẵn sàng, ứng dụng sử dụng mock data để demo.

## 🧪 Testing

```bash
# Chạy tests (nếu có)
npm run test

# Test coverage
npm run test:coverage
```

## 🚀 Deployment

### Build cho production:

```bash
npm run build
```

### Deploy lên Vercel:

```bash
# Cài đặt Vercel CLI
npm i -g vercel

# Deploy
vercel
```

### Deploy lên Netlify:

```bash
# Build
npm run build

# Upload thư mục dist/ lên Netlify
```

## 📱 Screenshots

### Trang chủ:

- Hero banner với carousel
- Grid danh mục sản phẩm
- Sản phẩm nổi bật
- Newsletter signup

### Trang sản phẩm:

- Filters sidebar
- Grid/List view toggle
- Pagination
- Sort options

### Chi tiết sản phẩm:

- Image gallery
- Product variants
- Add to cart
- Product description

### Giỏ hàng & Checkout:

- Cart management
- Shipping form
- Order summary
- Payment options

## 🤝 Đóng góp

1. Fork dự án
2. Tạo feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Mở Pull Request

## 📄 License

Distributed under the MIT License. See `LICENSE` for more information.

## 📞 Liên hệ

- **Email**: <EMAIL>
- **Website**: https://fashionstore.com
- **GitHub**: https://github.com/fashionstore

---

**Lưu ý**: Đây là phiên bản demo. Để sử dụng trong production, cần:

- Kết nối với backend thực tế
- Thêm authentication
- Tối ưu performance
- Thêm error boundary
- Cấu hình SEO
- Thêm analytics
