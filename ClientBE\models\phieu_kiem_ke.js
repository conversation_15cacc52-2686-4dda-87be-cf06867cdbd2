'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class PhieuKiemKe extends Model {
    static associate(models) {
      // Quan hệ với kho hàng
      PhieuKiemKe.belongsTo(models.KhoHang, {
        foreignKey: 'kho_hang_id',
        as: 'khoHang'
      });

      // Quan hệ với chi tiết kiểm kê (1-n)
      if (models.ChiTietKiemKe) {
        PhieuKiemKe.hasMany(models.ChiTietKiemKe, {
          foreignKey: 'phieu_kiem_ke_id',
          as: 'chiTietKiemKeList'
        });
      }

      // Quan hệ với lịch sử kho (1-n)
      if (models.LichSuKho) {
        PhieuKiemKe.hasMany(models.LichSuKho, {
          foreignKey: 'phieu_kiem_ke_id',
          as: 'lichSuKhoList'
        });
      }
    }
  }

  PhieuKiemKe.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    ma_kiem_ke: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      comment: 'M<PERSON> phiếu kiểm kê duy nhất'
    },
    ten_kiem_ke: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: 'Tên phiếu kiểm kê'
    },
    kho_hang_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'kho_hang',
        key: 'id'
      },
      comment: 'ID kho hàng được kiểm kê'
    },
    trang_thai: {
      type: DataTypes.ENUM('ke_hoach', 'dang_thuc_hien', 'hoan_thanh', 'huy'),
      allowNull: false,
      defaultValue: 'ke_hoach',
      comment: 'Trạng thái kiểm kê'
    },
    ngay_bat_dau: {
      type: DataTypes.DATE,
      allowNull: false,
      comment: 'Ngày bắt đầu kiểm kê'
    },
    ngay_ket_thuc: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'Ngày kết thúc kiểm kê'
    },
    nguoi_kiem_ke: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: 'Người thực hiện kiểm kê'
    },
    tong_san_pham: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: 'Tổng số sản phẩm cần kiểm kê'
    },
    san_pham_da_kiem: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: 'Số sản phẩm đã kiểm kê'
    },
    san_pham_lech: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: 'Số sản phẩm có chênh lệch'
    },
    gia_tri_lech: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      defaultValue: 0,
      comment: 'Giá trị chênh lệch (có thể âm)'
    },
    ghi_chu: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Ghi chú về phiếu kiểm kê'
    },
    nguoi_tao: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: 'Người tạo phiếu'
    },
    nguoi_cap_nhap: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: 'Người cập nhật cuối'
    },
    ngay_tao: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: 'Ngày tạo bản ghi'
    },
    ngay_cap_nhap: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: 'Ngày cập nhật bản ghi'
    }
  }, {
    sequelize,
    modelName: 'PhieuKiemKe',
    tableName: 'phieu_kiem_ke',
    timestamps: false // Sử dụng custom timestamp fields
  });

  return PhieuKiemKe;
};
