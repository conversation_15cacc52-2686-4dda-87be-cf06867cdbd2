'use strict';
const { Model } = require('sequelize');
const bcrypt = require('bcryptjs');

module.exports = (sequelize, DataTypes) => {
  class NguoiDung extends Model {
    static associate(models) {
      // Quan hệ với hồ sơ cá nhân (1-1)
      NguoiDung.hasOne(models.HoSoCaNhan, {
        foreignKey: 'nguoi_dung_id',
        as: 'hoSoCaNhan'
      });

      // Quan hệ với địa chỉ (1-n)
      NguoiDung.hasMany(models.DiaChiNguoiDung, {
        foreignKey: 'nguoi_dung_id',
        as: 'diaChiList'
      });

      // Quan hệ với nhóm khách hàng (n-n)
      NguoiDung.belongsToMany(models.NhomKhachHang, {
        through: models.NhomKhachHangNguoiDung,
        foreignKey: 'nguoi_dung_id',
        otherKey: 'nhom_khach_hang_id',
        as: 'nhomKhachHangList'
      });

      // <PERSON>uan hệ với vai trò (n-n)
      NguoiDung.belongsToMany(models.VaiTro, {
        through: models.NguoiDungVaiTro,
        foreignKey: 'nguoi_dung_id',
        otherKey: 'vai_tro_id',
        as: 'vaiTroList'
      });

      // Quan hệ với tag (n-n)
      NguoiDung.belongsToMany(models.Tag, {
        through: models.TagNguoiDung,
        foreignKey: 'nguoi_dung_id',
        otherKey: 'tag_id',
        as: 'tagList'
      });

      // Quan hệ với tích điểm (1-1)
      NguoiDung.hasOne(models.TichDiemNguoiDung, {
        foreignKey: 'nguoi_dung_id',
        as: 'tichDiem'
      });

      // Quan hệ với gợi ý bán hàng (1-1)
      NguoiDung.hasOne(models.GoiYBanHang, {
        foreignKey: 'nguoi_dung_id',
        as: 'goiYBanHang'
      });

      // Quan hệ với công nợ (1-1)
      NguoiDung.hasOne(models.CongNoNguoiDung, {
        foreignKey: 'nguoi_dung_id',
        as: 'congNo'
      });

      // Quan hệ với đơn hàng (khách hàng)
      NguoiDung.hasMany(models.DonHang, {
        foreignKey: 'khach_hang_id',
        as: 'donHangKhachHang'
      });

      // Quan hệ với đơn hàng (nhân viên bán)
      NguoiDung.hasMany(models.DonHang, {
        foreignKey: 'nhan_vien_ban_id',
        as: 'donHangNhanVien'
      });

      // Quan hệ với phân công nhân viên phụ trách (khách hàng)
      NguoiDung.hasMany(models.PhanCongNhanVienPhuTrach, {
        foreignKey: 'nguoi_dung_id',
        as: 'phanCongKhachHang'
      });

      // Quan hệ với phân công nhân viên phụ trách (nhân viên)
      NguoiDung.hasMany(models.PhanCongNhanVienPhuTrach, {
        foreignKey: 'nhan_vien_id',
        as: 'phanCongNhanVien'
      });

      // Quan hệ với trạng thái đơn hàng
      NguoiDung.hasMany(models.DonHangTrangThai, {
        foreignKey: 'nhan_vien_id',
        as: 'donHangTrangThaiList'
      });
    }

    // Instance methods
    async checkPassword(password) {
      return await bcrypt.compare(password, this.mat_khau);
    }

    toJSON() {
      const values = Object.assign({}, this.get());
      delete values.mat_khau;
      return values;
    }
  }

  NguoiDung.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    ho_ten: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [2, 100]
      }
    },
    so_dien_thoai: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        is: /^[0-9+\-\s()]*$/
      }
    },
    email: {
      type: DataTypes.STRING,
      allowNull: true,
      unique: true,
      validate: {
        isEmail: true
      }
    },
    mat_khau: {
      type: DataTypes.STRING,
      allowNull: true
    },
    loai_nguoi_dung: {
      type: DataTypes.ENUM('khach_hang', 'nhan_vien', 'admin'),
      defaultValue: 'khach_hang'
    },
    trang_thai: {
      type: DataTypes.ENUM('dang_giao_dich', 'ngung_giao_dich', 'tam_khoa'),
      defaultValue: 'dang_giao_dich'
    },
    nguoi_tao: {
      type: DataTypes.STRING,
      allowNull: true
    },
    nguoi_cap_nhap: {
      type: DataTypes.STRING,
      allowNull: true
    },
    ngay_tao: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    ngay_cap_nhap: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    sequelize,
    modelName: 'NguoiDung',
    tableName: 'nguoi_dung',
    timestamps: true,
    createdAt: 'ngay_tao',
    updatedAt: 'ngay_cap_nhap',
    hooks: {
      beforeCreate: async (user) => {
        if (user.mat_khau) {
          user.mat_khau = await bcrypt.hash(user.mat_khau, 12);
        }
      },
      beforeUpdate: async (user) => {
        if (user.changed('mat_khau') && user.mat_khau) {
          user.mat_khau = await bcrypt.hash(user.mat_khau, 12);
        }
      }
    }
  });

  return NguoiDung;
};
