'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class VaiTroQuyen extends Model {
    static associate(models) {
      // Quan hệ với vai trò
      VaiTroQuyen.belongsTo(models.VaiTro, {
        foreignKey: 'vai_tro_id',
        as: 'vaiTro'
      });

      // Quan hệ với quyền
      VaiTroQuyen.belongsTo(models.Quyen, {
        foreignKey: 'quyen_id',
        as: 'quyen'
      });
    }
  }

  VaiTroQuyen.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    vai_tro_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'vai_tro',
        key: 'id'
      }
    },
    quyen_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'quyen',
        key: 'id'
      }
    }
  }, {
    sequelize,
    modelName: 'VaiTroQuyen',
    tableName: 'vai_tro_quyen',
    timestamps: false,
    indexes: [
      {
        unique: true,
        fields: ['vai_tro_id', 'quyen_id'],
        name: 'vai_tro_quyen_unique'
      }
    ]
  });

  return VaiTroQuyen;
};
