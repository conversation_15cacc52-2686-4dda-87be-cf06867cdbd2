'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class CongNoNguoiDung extends Model {
    static associate(models) {
      // Quan hệ với người dùng (1-1)
      CongNoNguoiDung.belongsTo(models.NguoiDung, {
        foreignKey: 'nguoi_dung_id',
        as: 'nguoiDung'
      });
    }
  }

  CongNoNguoiDung.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    nguoi_dung_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      unique: true,
      references: {
        model: 'nguoi_dung',
        key: 'id'
      }
    },
    tong_cong_no: {
      type: DataTypes.DOUBLE,
      allowNull: true,
      defaultValue: 0
    },
    ghi_chu: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'CongNoNguoiDung',
    tableName: 'cong_no_nguoi_dung',
    timestamps: false
  });

  return CongNoNguoiDung;
};
